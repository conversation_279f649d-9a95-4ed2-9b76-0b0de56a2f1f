package com.geeksec.alarmprocessor.subscription;

import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.model.AlarmEvent;
import com.geeksec.alarmprocessor.subscription.manager.SubscriptionManager;
import com.geeksec.alarmprocessor.subscription.model.AlarmSubscription;
import com.geeksec.alarmprocessor.subscription.model.NotificationChannel;
import com.geeksec.alarmprocessor.subscription.model.NotificationTemplate;
import com.geeksec.alarmprocessor.subscription.model.SubscriptionRule;
import com.geeksec.alarmprocessor.subscription.notification.NotificationSender;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 订阅功能测试
 * 
 * <AUTHOR>
 */
public class SubscriptionTest {
    
    private static final Logger log = LoggerFactory.getLogger(SubscriptionTest.class);
    
    private SubscriptionManager subscriptionManager;
    private NotificationSender notificationSender;
    
    @BeforeEach
    public void setUp() {
        notificationSender = new NotificationSender();
        subscriptionManager = new SubscriptionManager(notificationSender);
    }
    
    @Test
    public void testSubscriptionCreation() {
        log.info("测试订阅创建");
        
        // 创建订阅
        AlarmSubscription subscription = AlarmSubscription.builder()
                .subscriptionId("test-subscription")
                .userId("test-user")
                .username("测试用户")
                .subscriptionName("测试订阅")
                .description("这是一个测试订阅")
                .enabled(true)
                .priority(AlarmSubscription.SubscriptionPriority.HIGH)
                .createTime(LocalDateTime.now())
                .build();
        
        // 添加规则
        SubscriptionRule rule = SubscriptionRule.createEqualsRule("alarmLevel", "HIGH");
        subscription.setRules(List.of(rule));
        
        // 添加通知渠道
        NotificationChannel channel = NotificationChannel.createEmailChannel("<EMAIL>", "default_email");
        subscription.setChannels(List.of(channel));
        
        // 验证订阅有效性
        assertTrue(subscription.isValid(), "订阅应该是有效的");
        
        // 添加到管理器
        subscriptionManager.addSubscription(subscription);
        
        // 验证添加成功
        AlarmSubscription retrieved = subscriptionManager.getSubscription("test-subscription");
        assertNotNull(retrieved, "应该能够检索到订阅");
        assertEquals("test-subscription", retrieved.getSubscriptionId());
        
        log.info("订阅创建测试通过");
    }
    
    @Test
    public void testSubscriptionRuleMatching() {
        log.info("测试订阅规则匹配");

        // 创建订阅
        AlarmSubscription subscription = createTestSubscription();
        subscriptionManager.addSubscription(subscription);

        // 创建匹配的告警
        Alarm matchingAlarm = createTestAlarm("HIGH", "恶意软件");

        // 调试信息
        log.info("告警级别: {}", matchingAlarm.getAlarmLevel());
        log.info("订阅规则: {}", subscription.getRules());

        // 测试匹配
        boolean matches = subscription.matches(matchingAlarm);
        log.info("匹配结果: {}", matches);

        assertTrue(matches, "告警应该匹配订阅规则");

        // 创建不匹配的告警
        Alarm nonMatchingAlarm = createTestAlarm("LOW", "正常流量");

        // 测试不匹配
        assertFalse(subscription.matches(nonMatchingAlarm), "告警不应该匹配订阅规则");

        log.info("订阅规则匹配测试通过");
    }
    
    @Test
    public void testAlarmProcessing() {
        log.info("测试告警处理");
        
        // 创建订阅
        AlarmSubscription subscription = createTestSubscription();
        subscriptionManager.addSubscription(subscription);
        
        // 创建告警
        Alarm alarm = createTestAlarm("HIGH", "恶意软件");
        
        // 处理告警
        SubscriptionManager.SubscriptionProcessResult result = subscriptionManager.processAlarm(alarm);
        
        // 验证处理结果
        assertTrue(result.isSuccess(), "告警处理应该成功");
        assertEquals(1, result.getMatchedCount(), "应该匹配1个订阅");
        assertFalse(result.getNotificationResults().isEmpty(), "应该有通知结果");
        
        log.info("告警处理测试通过");
    }
    
    @Test
    public void testNotificationTemplateRendering() {
        log.info("测试通知模板渲染");
        
        // 创建模板
        NotificationTemplate template = NotificationTemplate.createDefaultAlarmTemplate(
                NotificationChannel.ChannelType.EMAIL);
        
        // 验证模板有效性
        assertTrue(template.isValid(), "模板应该是有效的");
        
        // 准备上下文
        java.util.Map<String, Object> context = new java.util.HashMap<>();
        context.put("alarmType", "恶意软件检测");
        context.put("alarmName", "Trojan.Win32.Test");
        context.put("alarmLevel", "HIGH");
        context.put("threatType", "恶意软件");
        context.put("srcIp", "*************");
        context.put("dstIp", "********");
        context.put("protocol", "TCP");
        context.put("eventTimestamp", LocalDateTime.now());
        context.put("processedTimestamp", LocalDateTime.now());
        context.put("confidence", 0.95);
        context.put("riskScore", 85);
        context.put("description", "检测到恶意软件通信行为");
        
        // 渲染模板
        NotificationTemplate.RenderedTemplate rendered = template.render(context);
        
        // 验证渲染结果
        assertNotNull(rendered, "渲染结果不应该为空");
        assertNotNull(rendered.getTitle(), "标题不应该为空");
        assertNotNull(rendered.getContent(), "内容不应该为空");
        assertTrue(rendered.getTitle().contains("恶意软件检测"), "标题应该包含告警类型");
        assertTrue(rendered.getContent().contains("*************"), "内容应该包含源IP");
        
        log.info("通知模板渲染测试通过");
        log.info("渲染后的标题: {}", rendered.getTitle());
        log.info("渲染后的内容: {}", rendered.getContent());
    }
    
    @Test
    public void testNotificationChannelValidation() {
        log.info("测试通知渠道验证");
        
        // 测试有效的邮件渠道
        NotificationChannel emailChannel = NotificationChannel.createEmailChannel("<EMAIL>", "default_email");
        assertTrue(emailChannel.isAvailable(), "有效的邮件渠道应该可用");
        
        // 测试无效的邮件渠道
        NotificationChannel invalidEmailChannel = NotificationChannel.createEmailChannel("invalid-email", "default_email");
        assertFalse(invalidEmailChannel.isAvailable(), "无效的邮件渠道不应该可用");
        
        // 测试有效的短信渠道
        NotificationChannel smsChannel = NotificationChannel.createSmsChannel("13800138000", "default_sms");
        assertTrue(smsChannel.isAvailable(), "有效的短信渠道应该可用");
        
        // 测试无效的短信渠道
        NotificationChannel invalidSmsChannel = NotificationChannel.createSmsChannel("invalid-phone", "default_sms");
        assertFalse(invalidSmsChannel.isAvailable(), "无效的短信渠道不应该可用");
        
        log.info("通知渠道验证测试通过");
    }
    
    @Test
    public void testSubscriptionStatistics() {
        log.info("测试订阅统计");
        
        // 创建多个订阅
        for (int i = 0; i < 3; i++) {
            AlarmSubscription subscription = AlarmSubscription.builder()
                    .subscriptionId("subscription-" + i)
                    .userId("user-" + i)
                    .username("用户" + i)
                    .subscriptionName("订阅" + i)
                    .enabled(true)
                    .build();
            
            SubscriptionRule rule = SubscriptionRule.createEqualsRule("alarmLevel", "HIGH");
            subscription.setRules(List.of(rule));
            
            NotificationChannel channel = NotificationChannel.createEmailChannel("user" + i + "@example.com", "default_email");
            subscription.setChannels(List.of(channel));
            
            subscriptionManager.addSubscription(subscription);
        }
        
        // 获取统计信息
        SubscriptionManager.SubscriptionStatistics stats = subscriptionManager.getStatistics();
        
        // 验证统计信息
        assertEquals(3, stats.totalSubscriptions(), "应该有3个订阅");
        assertEquals(3, stats.totalUsers(), "应该有3个用户");
        assertTrue(stats.totalTemplates() > 0, "应该有模板");
        
        log.info("订阅统计测试通过");
        log.info("统计信息: 订阅数={}, 用户数={}, 模板数={}", 
                stats.totalSubscriptions(), stats.totalUsers(), stats.totalTemplates());
    }
    
    /**
     * 创建测试订阅
     */
    private AlarmSubscription createTestSubscription() {
        AlarmSubscription subscription = AlarmSubscription.builder()
                .subscriptionId("test-subscription")
                .userId("test-user")
                .username("测试用户")
                .subscriptionName("测试订阅")
                .description("这是一个测试订阅")
                .enabled(true)
                .priority(AlarmSubscription.SubscriptionPriority.HIGH)
                .createTime(LocalDateTime.now())
                .build();
        
        // 添加规则：告警级别为HIGH
        SubscriptionRule rule = SubscriptionRule.createEqualsRule("alarmLevel", AlarmEvent.AlarmLevel.HIGH);
        subscription.setRules(List.of(rule));
        
        // 添加通知渠道
        NotificationChannel channel = NotificationChannel.createEmailChannel("<EMAIL>", "default_email");
        subscription.setChannels(List.of(channel));
        
        return subscription;
    }
    
    /**
     * 创建测试告警
     */
    private Alarm createTestAlarm(String alarmLevel, String threatType) {
        return Alarm.builder()
                .alarmId("test-alarm-" + System.currentTimeMillis())
                .alarmType("威胁检测")
                .alarmName("测试告警")
                .alarmLevel(AlarmEvent.AlarmLevel.valueOf(alarmLevel))
                .threatType(threatType)
                .srcIp("*************")
                .dstIp("********")
                .protocol("TCP")
                .eventTimestamp(LocalDateTime.now())
                .processedTimestamp(LocalDateTime.now())
                .confidence(0.95)
                .description("这是一个测试告警")
                .build();
    }
}
