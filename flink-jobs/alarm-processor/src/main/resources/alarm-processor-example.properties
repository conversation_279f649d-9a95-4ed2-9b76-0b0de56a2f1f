# ==================== 告警处理器配置示例 ====================
# 此文件展示了告警处理器的完整配置选项
# 复制此文件为 alarm-processor.properties 并根据实际环境修改配置

# ==================== 作业基础配置 ====================
alarm.processor.job.name=alarm-processor
alarm.processor.job.parallelism=4
alarm.processor.job.checkpointInterval=60000
alarm.processor.job.restartAttempts=3
alarm.processor.job.restartDelay=10000

# ==================== Kafka 配置 ====================
# Kafka 集群地址
kafka.bootstrap.servers=localhost:9092
alarm.processor.kafka.bootstrapServers=${kafka.bootstrap.servers}

# 消费者组ID
kafka.group.id=alarm-processor-group
alarm.processor.kafka.groupId=${kafka.group.id}

# 主题配置
alarm.processor.kafka.input.topic=raw-alarms
alarm.processor.kafka.output.topic=processed-alarms
alarm.processor.kafka.notification.topic=alarm-notifications

# 消费配置
alarm.processor.kafka.input.startingOffsets=latest
alarm.processor.kafka.input.autoCommit=true
alarm.processor.kafka.input.commitInterval=5000

# ==================== PostgreSQL 配置 ====================
postgresql.host=localhost
postgresql.port=5432
postgresql.database=nta
postgresql.username=nta_user
postgresql.password=nta_password

alarm.processor.postgresql.host=${postgresql.host}
alarm.processor.postgresql.port=${postgresql.port}
alarm.processor.postgresql.database=${postgresql.database}
alarm.processor.postgresql.username=${postgresql.username}
alarm.processor.postgresql.password=${postgresql.password}

# ==================== 告警处理配置 ====================
# 去重配置
alarm.processor.processing.deduplication.enabled=true
alarm.processor.processing.deduplication.mode=HASH_BASED
alarm.processor.processing.deduplication.timeWindowMs=300000
alarm.processor.processing.deduplication.maxCacheSize=10000
alarm.processor.processing.deduplication.cacheExpirationMs=600000

# 格式化配置
alarm.processor.processing.formatting.enabled=true
alarm.processor.processing.formatting.includeReasonAnalysis=true
alarm.processor.processing.formatting.includeHandlingSuggestions=true

# 攻击链分析配置
alarm.processor.processing.attackChain.enabled=true
alarm.processor.processing.attackChain.correlationWindowMs=1800000
alarm.processor.processing.attackChain.maxCacheSize=5000
alarm.processor.processing.attackChain.minEventsForChain=2

# 批量处理配置
alarm.processor.processing.batch.enabled=true
alarm.processor.processing.batch.maxBatchSize=50
alarm.processor.processing.batch.maxWaitTimeMs=30000
alarm.processor.processing.batch.checkIntervalMs=5000

# ==================== 并行度配置 ====================
# 各组件并行度
parallelism.kafka.source=4
parallelism.processing=8
alarm.processor.parallelism.kafka.source=${parallelism.kafka.source}
alarm.processor.parallelism.processing=${parallelism.processing}
alarm.processor.parallelism.postgresql.sink=2
alarm.processor.parallelism.notification.sink=2
alarm.processor.parallelism.notification.processing=2

# ==================== 监控配置 ====================
# 监控开关
alarm.processor.monitoring.enabled=true
alarm.processor.monitoring.metricsInterval=30000
alarm.processor.monitoring.performanceLogging=true
alarm.processor.monitoring.detailedMetrics=false

# 通知统计
alarm.processor.notification.statisticsIntervalSeconds=300

# ==================== 白名单配置 ====================
# 白名单功能
alarm.processor.whitelist.enabled=false
alarm.processor.whitelist.configPath=alarm_whitelist.json
alarm.processor.whitelist.updateInterval=300

# ==================== 开发和调试配置 ====================
# 日志级别（在 logback.xml 中配置）
# 开发模式下可以启用详细日志

# 测试模式配置
# alarm.processor.test.mode=true
# alarm.processor.test.mockNotifications=true

# ==================== 高级配置 ====================
# JVM 参数建议（在启动脚本中设置）
# -Xmx4g -Xms4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+PrintGCDetails
# -XX:+PrintGCTimeStamps

# Flink 配置建议
# taskmanager.memory.process.size=4g
# taskmanager.numberOfTaskSlots=4
# parallelism.default=4
# state.backend=rocksdb
# state.checkpoints.dir=file:///path/to/checkpoints

# ==================== 示例订阅配置 ====================
# 以下是一些示例订阅配置，实际使用时通过 REST API 创建

# 示例1：高危恶意软件告警邮件通知
# 订阅名称：高危恶意软件告警
# 匹配规则：alarmType=恶意软件 AND alarmLevel>=HIGH
# 通知渠道：email:<EMAIL>

# 示例2：内网横向移动告警Kafka通知
# 订阅名称：内网横向移动告警
# 匹配规则：attackFamily=横向移动 AND srcIp CONTAINS 192.168
# 通知渠道：kafka:lateral-movement-alerts

# 示例3：外网攻击告警综合通知
# 订阅名称：外网攻击告警
# 匹配规则：srcIp NOT CONTAINS 192.168 AND srcIp NOT CONTAINS 10.0
# 通知渠道：email:<EMAIL>, kafka:external-attacks

# ==================== 配置验证 ====================
# 启动时会验证以下配置项：
# 1. Kafka 连接性
# 2. PostgreSQL 连接性
# 3. 邮件服务器连接性（如果启用邮件通知）
# 4. 告警服务连接性
# 5. 必要的主题是否存在

# ==================== 性能调优建议 ====================
# 1. 根据告警量调整并行度
# 2. 根据内存大小调整缓存大小
# 3. 根据网络延迟调整超时时间
# 4. 根据存储性能调整批量大小
# 5. 定期监控检查点大小和恢复时间
