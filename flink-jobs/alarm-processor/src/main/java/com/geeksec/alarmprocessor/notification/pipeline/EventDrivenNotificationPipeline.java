package com.geeksec.alarmprocessor.notification.pipeline;

import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.notification.client.AlarmServiceClient;
import com.geeksec.alarmprocessor.notification.function.NotificationFunction;
import com.geeksec.alarmprocessor.notification.source.SubscriptionConfigSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/**
 * 事件驱动的通知处理流水线
 * 使用 Kafka 事件驱动的订阅配置同步机制
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class EventDrivenNotificationPipeline {
    
    private final AlarmProcessorConfig config;
    private final AlarmServiceClient alarmServiceClient;
    
    public EventDrivenNotificationPipeline(AlarmProcessorConfig config) {
        this.config = config;
        this.alarmServiceClient = new AlarmServiceClient(config.getAlarmServiceBaseUrl());
        log.info("事件驱动通知处理流水线初始化完成");
    }
    
    /**
     * 构建通知处理流水线
     * 
     * @param alarmStream 告警数据流
     * @param env Flink执行环境
     * @return 通知结果数据流
     */
    public DataStream<String> buildNotificationPipeline(DataStream<Alarm> alarmStream,
                                                        StreamExecutionEnvironment env) {
        log.info("开始构建事件驱动通知处理流水线");
        
        // 1. 创建初始订阅配置源（启动时获取全量配置）
        DataStream<String> initialConfigStream = createInitialConfigStream(env);
        
        // 2. 创建 Kafka 订阅变更事件源
        DataStream<String> changeEventStream = createSubscriptionChangeEventStream(env);
        
        // 3. 合并初始配置和变更事件
        DataStream<String> allSubscriptionEvents = initialConfigStream.union(changeEventStream);
        
        // 4. 创建广播流
        BroadcastStream<String> subscriptionBroadcastStream = 
                allSubscriptionEvents.broadcast(NotificationFunction.SUBSCRIPTION_STATE_DESCRIPTOR);
        
        // 5. 连接告警流和订阅配置广播流，处理通知
        SingleOutputStreamOperator<String> notificationResultStream = alarmStream
                .connect(subscriptionBroadcastStream)
                .process(new EventDrivenNotificationFunction(config, alarmServiceClient))
                .name("event-driven-notification-processor")
                .setParallelism(config.getNotificationParallelism());
        
        log.info("事件驱动通知处理流水线构建完成");
        return notificationResultStream;
    }
    
    /**
     * 创建初始配置流（启动时获取全量配置）
     */
    private DataStream<String> createInitialConfigStream(StreamExecutionEnvironment env) {
        return env.addSource(new InitialSubscriptionConfigSource(alarmServiceClient))
                .name("initial-subscription-config-source")
                .setParallelism(1);
    }
    
    /**
     * 创建订阅变更事件流
     */
    private DataStream<String> createSubscriptionChangeEventStream(StreamExecutionEnvironment env) {
        KafkaSource<String> kafkaSource = KafkaSource.<String>builder()
                .setBootstrapServers(config.getKafkaConfig().getBootstrapServers())
                .setTopics("subscription-changes")
                .setGroupId("alarm-processor-subscription-group")
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();
        
        return env.fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "subscription-change-events")
                .setParallelism(1);
    }
    
    /**
     * 初始订阅配置源
     */
    private static class InitialSubscriptionConfigSource 
            extends org.apache.flink.streaming.api.functions.source.RichSourceFunction<String> {
        
        private static final long serialVersionUID = 1L;
        
        private final AlarmServiceClient alarmServiceClient;
        private volatile boolean running = true;
        
        public InitialSubscriptionConfigSource(AlarmServiceClient alarmServiceClient) {
            this.alarmServiceClient = alarmServiceClient;
        }
        
        @Override
        public void run(SourceContext<String> ctx) throws Exception {
            log.info("开始加载初始订阅配置...");
            
            try {
                // 获取全量订阅配置
                java.util.List<String> subscriptions = alarmServiceClient.getAllActiveSubscriptionsAsJson()
                        .get(30, java.util.concurrent.TimeUnit.SECONDS);
                
                if (subscriptions != null && !subscriptions.isEmpty()) {
                    synchronized (ctx.getCheckpointLock()) {
                        for (String subscription : subscriptions) {
                            if (running) {
                                ctx.collect(subscription);
                            }
                        }
                    }
                    log.info("初始订阅配置加载完成，数量: {}", subscriptions.size());
                } else {
                    log.warn("未获取到初始订阅配置");
                }
                
            } catch (Exception e) {
                log.error("加载初始订阅配置失败", e);
            }
            
            log.info("初始订阅配置源完成");
        }
        
        @Override
        public void cancel() {
            running = false;
        }
    }
    
    /**
     * 事件驱动通知处理函数
     */
    private static class EventDrivenNotificationFunction 
            extends org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction<Alarm, String, String> {
        
        private static final long serialVersionUID = 1L;
        
        private final AlarmProcessorConfig config;
        private final AlarmServiceClient alarmServiceClient;
        
        public EventDrivenNotificationFunction(AlarmProcessorConfig config, AlarmServiceClient alarmServiceClient) {
            this.config = config;
            this.alarmServiceClient = alarmServiceClient;
        }
        
        @Override
        public void processElement(Alarm alarm, ReadOnlyContext ctx, 
                                 org.apache.flink.util.Collector<String> out) throws Exception {
            // 处理告警，匹配订阅并发送通知
            log.debug("处理告警: {}", alarm.getAlarmId());
            
            // 这里简化实现，实际应该根据广播状态中的订阅配置进行匹配和通知
            // 暂时输出处理结果
            out.collect("处理告警: " + alarm.getAlarmId());
        }
        
        @Override
        public void processBroadcastElement(String subscriptionEvent, Context ctx, 
                                          org.apache.flink.util.Collector<String> out) throws Exception {
            // 处理订阅配置变更事件
            log.debug("收到订阅配置变更: {}", subscriptionEvent);
            
            // 更新广播状态
            // 这里需要解析 subscriptionEvent 并更新状态
            // 暂时简化实现
        }
    }
    
    /**
     * 创建默认事件驱动通知流水线
     */
    public static EventDrivenNotificationPipeline createDefault(AlarmProcessorConfig config) {
        return new EventDrivenNotificationPipeline(config);
    }
}
