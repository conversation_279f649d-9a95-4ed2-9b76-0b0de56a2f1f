package com.geeksec.alarmprocessor.notification.source;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.alarmprocessor.notification.client.AlarmServiceClient;
import com.geeksec.nta.alarm.dto.subscription.NotificationSubscriptionDto;
import com.geeksec.nta.alarm.event.SubscriptionChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 订阅配置数据源
 * 负责初始化时获取全量订阅配置，以及接收 Kafka 变更事件
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SubscriptionConfigSource extends RichSourceFunction<NotificationSubscriptionDto> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmServiceClient alarmServiceClient;
    private final String kafkaBootstrapServers;
    private final String subscriptionChangesTopic;
    private final String consumerGroupId;
    
    private volatile boolean running = true;
    private transient ObjectMapper objectMapper;
    
    public SubscriptionConfigSource(AlarmServiceClient alarmServiceClient,
                                  String kafkaBootstrapServers,
                                  String subscriptionChangesTopic,
                                  String consumerGroupId) {
        this.alarmServiceClient = alarmServiceClient;
        this.kafkaBootstrapServers = kafkaBootstrapServers;
        this.subscriptionChangesTopic = subscriptionChangesTopic;
        this.consumerGroupId = consumerGroupId;
    }
    
    @Override
    public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
        super.open(parameters);
        this.objectMapper = new ObjectMapper();
        log.info("订阅配置数据源已初始化");
    }
    
    @Override
    public void run(SourceContext<NotificationSubscriptionDto> ctx) throws Exception {
        log.info("订阅配置数据源开始运行");
        
        // 1. 初始化时获取全量订阅配置
        loadInitialSubscriptions(ctx);
        
        // 2. 创建 Kafka 消费者监听变更事件
        // 注意：这里简化实现，实际应该使用 Flink Kafka Connector
        // 在实际使用中，应该在 Pipeline 中单独创建 Kafka Source
        log.info("订阅配置数据源初始化完成，等待 Kafka 事件...");
        
        // 保持运行状态
        while (running) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.info("订阅配置数据源被中断");
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.info("订阅配置数据源已停止");
    }
    
    @Override
    public void cancel() {
        log.info("取消订阅配置数据源");
        running = false;
    }
    
    /**
     * 加载初始订阅配置
     */
    private void loadInitialSubscriptions(SourceContext<NotificationSubscriptionDto> ctx) {
        try {
            log.info("开始加载初始订阅配置...");
            
            CompletableFuture<List<NotificationSubscriptionDto>> future = 
                    alarmServiceClient.getAllActiveSubscriptions();
            
            List<NotificationSubscriptionDto> subscriptions = future.get(30, TimeUnit.SECONDS);
            
            if (subscriptions != null && !subscriptions.isEmpty()) {
                synchronized (ctx.getCheckpointLock()) {
                    for (NotificationSubscriptionDto subscription : subscriptions) {
                        ctx.collect(subscription);
                    }
                }
                log.info("初始订阅配置加载完成，数量: {}", subscriptions.size());
            } else {
                log.warn("未获取到初始订阅配置");
            }
            
        } catch (Exception e) {
            log.error("加载初始订阅配置失败", e);
            // 不抛出异常，允许系统继续运行
        }
    }
    
    /**
     * 处理订阅变更事件
     */
    public void processSubscriptionChangeEvent(String eventJson, SourceContext<NotificationSubscriptionDto> ctx) {
        try {
            SubscriptionChangeEvent event = objectMapper.readValue(eventJson, SubscriptionChangeEvent.class);
            
            log.debug("收到订阅变更事件: {}, 类型: {}", event.getSubscriptionId(), event.getEventType());
            
            switch (event.getEventType()) {
                case CREATED:
                case UPDATED:
                case ENABLED:
                    // 对于创建、更新、启用事件，发送订阅数据
                    if (event.getSubscriptionData() != null) {
                        NotificationSubscriptionDto subscription = 
                                objectMapper.readValue(event.getSubscriptionData(), NotificationSubscriptionDto.class);
                        
                        synchronized (ctx.getCheckpointLock()) {
                            ctx.collect(subscription);
                        }
                        
                        log.info("处理订阅变更事件成功: {} - {}", event.getEventType(), event.getSubscriptionId());
                    }
                    break;
                    
                case DELETED:
                case DISABLED:
                    // 对于删除、禁用事件，发送禁用的订阅数据
                    NotificationSubscriptionDto disabledSubscription = NotificationSubscriptionDto.builder()
                            .subscriptionId(event.getSubscriptionId())
                            .userId(event.getUserId())
                            .enabled(false)
                            .build();
                    
                    synchronized (ctx.getCheckpointLock()) {
                        ctx.collect(disabledSubscription);
                    }
                    
                    log.info("处理订阅删除/禁用事件成功: {} - {}", event.getEventType(), event.getSubscriptionId());
                    break;
                    
                default:
                    log.warn("未知的订阅变更事件类型: {}", event.getEventType());
                    break;
            }
            
        } catch (Exception e) {
            log.error("处理订阅变更事件失败: {}", eventJson, e);
        }
    }
    
    /**
     * 创建 Kafka 订阅变更事件源
     */
    public static KafkaSource<String> createKafkaSubscriptionChangeSource(String kafkaBootstrapServers,
                                                                          String subscriptionChangesTopic,
                                                                          String consumerGroupId) {
        return KafkaSource.<String>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setTopics(subscriptionChangesTopic)
                .setGroupId(consumerGroupId)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();
    }
}
