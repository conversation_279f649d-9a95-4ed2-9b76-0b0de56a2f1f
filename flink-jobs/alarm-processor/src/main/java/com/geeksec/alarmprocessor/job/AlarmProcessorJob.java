package com.geeksec.alarmprocessor.job;

import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.AlarmEvent;
import com.geeksec.alarmprocessor.output.AlarmOutputManager;
import com.geeksec.alarmprocessor.pipeline.AlarmProcessingPipeline;
import com.geeksec.alarmprocessor.source.AlarmEventSourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.runtime.state.hashmap.HashMapStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/**
 * 告警处理作业主类
 * 
 * 业务流程：
 * 1. 从 Kafka 接收来自各个检测模块的告警事件
 * 2. 进行统一的告警处理：转换、去重、格式化、攻击链分析
 * 3. 生成多种输出：
 *    - 告警数据写入 Doris 表
 *    - 告警通知发送到 Kafka
 *    - 高优先级告警特殊处理
 *    - 证书告警特殊处理
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmProcessorJob {
    
    public static void main(String[] args) throws Exception {
        log.info("启动告警处理作业");
        
        try {
            // 1. 获取配置（统一配置管理器 + 命令行参数）
            final ParameterTool parameterTool = ParameterTool.fromArgs(args)
                    .mergeWith(AlarmProcessorConfig.getConfig());

            // 2. 创建配置对象
            final AlarmProcessorConfig config = AlarmProcessorConfig.create();

            // 打印配置信息
            config.printConfig();

            // 3. 创建执行环境
            final StreamExecutionEnvironment env = createExecutionEnvironment(config);

            // 4. 设置全局参数
            env.getConfig().setGlobalJobParameters(parameterTool);

            // 5. 配置检查点
            configureCheckpointing(env, config);

            // 6. 创建告警事件数据源
            DataStream<AlarmEvent> alarmEventStream = AlarmEventSourceFactory
                    .createAlarmEventSource(env, config);

            // 7. 构建告警处理流水线
            AlarmProcessingPipeline.PipelineResult pipelineResult =
                    AlarmProcessingPipeline.build(alarmEventStream, config, env);

            // 8. 配置输出管理器
            AlarmOutputManager.configureAllOutputs(pipelineResult, config);

            // 9. 添加调试输出（如果启用）
            if (config.isMonitoringEnabled() && config.isPerformanceLogging()) {
                AlarmOutputManager.addDebugOutputs(pipelineResult, config);
            }

            // 10. 执行作业
            log.info("开始执行告警处理作业...");
            env.execute(config.getJobName());
            
            log.info("告警处理作业执行完成");
            
        } catch (Exception e) {
            log.error("告警处理作业执行失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 创建执行环境
     */
    private static StreamExecutionEnvironment createExecutionEnvironment(AlarmProcessorConfig config) {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        env.setParallelism(config.getJobParallelism());
        
        // 设置状态后端
        env.setStateBackend(new HashMapStateBackend());
        
        // 设置重启策略
        env.setRestartStrategy(
                org.apache.flink.api.common.restartstrategy.RestartStrategies
                        .fixedDelayRestart(
                                config.getRestartAttempts(),
                                org.apache.flink.api.common.time.Time.milliseconds(config.getRestartDelay())
                        )
        );
        
        log.info("执行环境创建完成，并行度: {}", config.getJobParallelism());
        return env;
    }
    
    /**
     * 配置检查点
     */
    private static void configureCheckpointing(StreamExecutionEnvironment env, AlarmProcessorConfig config) {
        // 启用检查点
        env.enableCheckpointing(config.getCheckpointInterval());
        
        // 检查点配置
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        
        // 设置检查点模式为精确一次
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        
        // 设置检查点之间的最小间隔
        checkpointConfig.setMinPauseBetweenCheckpoints(config.getCheckpointInterval() / 2);
        
        // 设置检查点超时时间
        checkpointConfig.setCheckpointTimeout(600000); // 10分钟
        
        // 设置最大并发检查点数
        checkpointConfig.setMaxConcurrentCheckpoints(1);
        
        // 设置检查点失败时是否让任务失败
        checkpointConfig.setTolerableCheckpointFailureNumber(3);
        
        // 设置作业取消时保留检查点
        checkpointConfig.setExternalizedCheckpointCleanup(
                CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        
        // 启用非对齐检查点（提高性能）
        checkpointConfig.enableUnalignedCheckpoints(true);
        
        log.info("检查点配置完成，间隔: {}ms", config.getCheckpointInterval());
    }
    
    /**
     * 打印作业信息
     */
    private static void printJobInfo(AlarmProcessorConfig config) {
        log.info("=== 告警处理作业信息 ===");
        log.info("作业名称: {}", config.getJobName());
        log.info("作业并行度: {}", config.getJobParallelism());
        log.info("输入主题: {}", config.getInputTopic());
        log.info("输出主题: {}", config.getOutputTopic());
        log.info("通知主题: {}", config.getNotificationTopic());
        log.info("Kafka 服务器: {}", config.getKafkaBootstrapServers());
        log.info("Doris 节点: {}", config.getDorisFeNodes());
        log.info("目标数据库表: {}.{}", config.getDorisDatabase(), config.getDorisTable());
        log.info("去重启用: {}", config.isDeduplicationEnabled());
        log.info("格式化启用: {}", config.isFormattingEnabled());
        log.info("攻击链分析启用: {}", config.isAttackChainEnabled());
        log.info("监控启用: {}", config.isMonitoringEnabled());
        log.info("========================");
    }
    
    /**
     * 验证配置
     */
    private static void validateConfig(AlarmProcessorConfig config) {
        if (config.getKafkaBootstrapServers() == null || config.getKafkaBootstrapServers().trim().isEmpty()) {
            throw new IllegalArgumentException("Kafka Bootstrap Servers 不能为空");
        }
        
        if (config.getInputTopic() == null || config.getInputTopic().trim().isEmpty()) {
            throw new IllegalArgumentException("输入主题不能为空");
        }
        
        if (config.isDorisEnabled()) {
            if (config.getDorisFeNodes() == null || config.getDorisFeNodes().trim().isEmpty()) {
                throw new IllegalArgumentException("Doris FE 节点不能为空");
            }
            
            if (config.getDorisDatabase() == null || config.getDorisDatabase().trim().isEmpty()) {
                throw new IllegalArgumentException("Doris 数据库名不能为空");
            }
            
            if (config.getDorisTable() == null || config.getDorisTable().trim().isEmpty()) {
                throw new IllegalArgumentException("Doris 表名不能为空");
            }
        }
        
        if (config.getJobParallelism() <= 0) {
            throw new IllegalArgumentException("作业并行度必须大于0");
        }
        
        if (config.getCheckpointInterval() <= 0) {
            throw new IllegalArgumentException("检查点间隔必须大于0");
        }
        
        log.info("配置验证通过");
    }
    
    /**
     * 注册关闭钩子
     */
    private static void registerShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("接收到关闭信号，正在优雅关闭告警处理作业...");
            // 这里可以添加清理逻辑
            log.info("告警处理作业已关闭");
        }));
    }
}
