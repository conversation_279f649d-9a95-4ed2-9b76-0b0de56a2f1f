package com.geeksec.alarmnotification.function;

import com.geeksec.alarmnotification.config.AlarmNotificationConfig;
import com.geeksec.alarmnotification.dto.NotificationChannelDto;
import com.geeksec.alarmnotification.dto.NotificationResultDto;
import com.geeksec.alarmnotification.dto.NotificationSubscriptionDto;
import com.geeksec.alarmnotification.dto.SubscriptionRuleDto;
import com.geeksec.alarmnotification.model.Alarm;
import com.geeksec.alarmnotification.sender.NotificationSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

/**
 * 通知处理函数
 * 处理告警事件和订阅配置的广播流，实现告警通知发送
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class NotificationFunction extends BroadcastProcessFunction<Alarm, NotificationSubscriptionDto, NotificationResultDto> {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅配置状态描述符
     */
    public static final MapStateDescriptor<String, NotificationSubscriptionDto> SUBSCRIPTION_STATE_DESCRIPTOR =
            new MapStateDescriptor<>("subscription-state", String.class, NotificationSubscriptionDto.class);
    
    private final AlarmNotificationConfig config;
    private transient NotificationSender notificationSender;
    
    public NotificationFunction(AlarmNotificationConfig config) {
        this.config = config;
    }
    
    @Override
    public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
        super.open(parameters);
        this.notificationSender = new NotificationSender(config);
        log.info("通知处理函数已初始化");
    }
    
    @Override
    public void close() throws Exception {
        if (notificationSender != null) {
            notificationSender.close();
        }
        super.close();
    }
    
    @Override
    public void processElement(Alarm alarm, ReadOnlyContext ctx, Collector<NotificationResultDto> out) throws Exception {
        ReadOnlyBroadcastState<String, NotificationSubscriptionDto> subscriptionState = 
                ctx.getBroadcastState(SUBSCRIPTION_STATE_DESCRIPTOR);
        
        log.debug("处理告警通知: {}", alarm.getAlarmId());
        
        List<NotificationSubscriptionDto> matchedSubscriptions = new ArrayList<>();
        
        // 遍历所有订阅，找到匹配的订阅
        for (Map.Entry<String, NotificationSubscriptionDto> entry : subscriptionState.immutableEntries()) {
            NotificationSubscriptionDto subscription = entry.getValue();
            
            if (Boolean.TRUE.equals(subscription.getEnabled()) && matchesSubscription(alarm, subscription)) {
                matchedSubscriptions.add(subscription);
                log.debug("告警 {} 匹配订阅: {}", alarm.getAlarmId(), subscription.getSubscriptionId());
            }
        }
        
        if (matchedSubscriptions.isEmpty()) {
            log.debug("告警 {} 未匹配任何订阅", alarm.getAlarmId());
            return;
        }
        
        // 发送通知
        for (NotificationSubscriptionDto subscription : matchedSubscriptions) {
            sendNotifications(alarm, subscription, out);
        }
    }
    
    @Override
    public void processBroadcastElement(NotificationSubscriptionDto subscription, Context ctx, Collector<NotificationResultDto> out) throws Exception {
        BroadcastState<String, NotificationSubscriptionDto> subscriptionState = 
                ctx.getBroadcastState(SUBSCRIPTION_STATE_DESCRIPTOR);
        
        if (Boolean.TRUE.equals(subscription.getEnabled())) {
            subscriptionState.put(subscription.getSubscriptionId(), subscription);
            log.debug("更新订阅配置: {}", subscription.getSubscriptionId());
        } else {
            subscriptionState.remove(subscription.getSubscriptionId());
            log.debug("移除订阅配置: {}", subscription.getSubscriptionId());
        }
    }
    
    /**
     * 检查告警是否匹配订阅规则
     */
    private boolean matchesSubscription(Alarm alarm, NotificationSubscriptionDto subscription) {
        if (subscription.getRules() == null || subscription.getRules().isEmpty()) {
            return true; // 没有规则则匹配所有告警
        }
        
        // 所有规则都必须匹配（AND逻辑）
        for (SubscriptionRuleDto rule : subscription.getRules()) {
            if (!matchesRule(alarm, rule)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查告警是否匹配单个规则
     */
    private boolean matchesRule(Alarm alarm, SubscriptionRuleDto rule) {
        String fieldValue = getFieldValue(alarm, rule.getFieldName());
        if (fieldValue == null) {
            return false;
        }
        
        String expectedValue = rule.getExpectedValue();
        if (expectedValue == null) {
            return false;
        }
        
        // 处理大小写忽略
        if (Boolean.TRUE.equals(rule.getIgnoreCase())) {
            fieldValue = fieldValue.toLowerCase();
            expectedValue = expectedValue.toLowerCase();
        }
        
        switch (rule.getOperator()) {
            case EQUALS:
                return fieldValue.equals(expectedValue);
            case NOT_EQUALS:
                return !fieldValue.equals(expectedValue);
            case CONTAINS:
                return fieldValue.contains(expectedValue);
            case NOT_CONTAINS:
                return !fieldValue.contains(expectedValue);
            case REGEX:
                try {
                    Pattern pattern = Pattern.compile(expectedValue);
                    return pattern.matcher(fieldValue).matches();
                } catch (Exception e) {
                    log.warn("正则表达式匹配失败: {}", expectedValue, e);
                    return false;
                }
            case IN:
                String[] values = expectedValue.split(",");
                for (String value : values) {
                    if (fieldValue.equals(value.trim())) {
                        return true;
                    }
                }
                return false;
            case NOT_IN:
                String[] notInValues = expectedValue.split(",");
                for (String value : notInValues) {
                    if (fieldValue.equals(value.trim())) {
                        return false;
                    }
                }
                return true;
            default:
                log.warn("不支持的操作符: {}", rule.getOperator());
                return false;
        }
    }
    
    /**
     * 获取告警字段值
     */
    private String getFieldValue(Alarm alarm, String fieldName) {
        switch (fieldName) {
            case "alarmType":
                return alarm.getAlarmType();
            case "alarmLevel":
                return alarm.getAlarmLevel() != null ? alarm.getAlarmLevel().name() : null;
            case "sourceModule":
                return alarm.getSourceModule();
            case "srcIp":
                return alarm.getSrcIp();
            case "dstIp":
                return alarm.getDstIp();
            case "threatType":
                return alarm.getThreatType();
            case "alarmName":
                return alarm.getAlarmName();
            case "detectorType":
                return alarm.getDetectorType();
            case "protocol":
                return alarm.getProtocol();
            default:
                // 尝试从扩展属性中获取
                if (alarm.getExtendedProperties() != null) {
                    Object value = alarm.getExtendedProperties().get(fieldName);
                    return value != null ? value.toString() : null;
                }
                return null;
        }
    }
    
    /**
     * 发送通知到所有渠道
     */
    private void sendNotifications(Alarm alarm, NotificationSubscriptionDto subscription, Collector<NotificationResultDto> out) {
        if (subscription.getChannels() == null || subscription.getChannels().isEmpty()) {
            log.debug("订阅 {} 没有配置通知渠道", subscription.getSubscriptionId());
            return;
        }
        
        List<NotificationChannelDto> channels = subscription.getChannels();
        
        for (NotificationChannelDto channel : channels) {
            if (!Boolean.TRUE.equals(channel.getEnabled())) {
                continue;
            }
            
            // 准备通知内容
            String subject = prepareNotificationSubject(alarm, subscription);
            String content = prepareNotificationContent(alarm, subscription);
            
            // 异步发送通知
            CompletableFuture<NotificationSender.NotificationResult> future = 
                    notificationSender.sendNotification(channel, subject, content);
            
            // 处理发送结果
            future.whenComplete((result, throwable) -> {
                NotificationResultDto resultDto;
                if (throwable != null) {
                    resultDto = NotificationResultDto.failure(
                            subscription.getSubscriptionId(),
                            alarm.getAlarmId(),
                            channel.getChannelType(),
                            channel.getAddress(),
                            throwable.getMessage(),
                            0
                    );
                } else if (result.isSuccess()) {
                    resultDto = NotificationResultDto.success(
                            subscription.getSubscriptionId(),
                            alarm.getAlarmId(),
                            channel.getChannelType(),
                            channel.getAddress(),
                            subject,
                            content
                    );
                } else {
                    resultDto = NotificationResultDto.failure(
                            subscription.getSubscriptionId(),
                            alarm.getAlarmId(),
                            channel.getChannelType(),
                            channel.getAddress(),
                            result.getMessage(),
                            0
                    );
                }
                
                out.collect(resultDto);
            });
        }
    }
    
    /**
     * 准备通知主题
     */
    private String prepareNotificationSubject(Alarm alarm, NotificationSubscriptionDto subscription) {
        return String.format("[%s] %s - %s", 
                alarm.getAlarmLevel(), 
                alarm.getAlarmType(), 
                alarm.getAlarmName());
    }
    
    /**
     * 准备通知内容
     */
    private String prepareNotificationContent(Alarm alarm, NotificationSubscriptionDto subscription) {
        StringBuilder content = new StringBuilder();
        content.append("告警详情:\n");
        content.append("告警ID: ").append(alarm.getAlarmId()).append("\n");
        content.append("告警类型: ").append(alarm.getAlarmType()).append("\n");
        content.append("告警级别: ").append(alarm.getAlarmLevel()).append("\n");
        content.append("威胁类型: ").append(alarm.getThreatType()).append("\n");
        content.append("源IP: ").append(alarm.getSrcIp()).append("\n");
        content.append("目标IP: ").append(alarm.getDstIp()).append("\n");
        content.append("告警时间: ").append(alarm.getEventTimestamp()).append("\n");
        content.append("告警描述: ").append(alarm.getDescription()).append("\n");
        
        return content.toString();
    }
}
