package com.geeksec.alarmnotification.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 通知订阅 DTO（供 alarm-notification 使用）
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationSubscriptionDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅ID
     */
    private String subscriptionId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 订阅名称
     */
    private String subscriptionName;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 优先级
     */
    private Integer priorityLevel;
    
    /**
     * 匹配规则列表
     */
    private List<SubscriptionRuleDto> rules;
    
    /**
     * 通知渠道列表
     */
    private List<NotificationChannelDto> channels;
    
    /**
     * 通知频率类型
     */
    private FrequencyType frequencyType;
    
    /**
     * 频率控制配置
     */
    private FrequencyConfigDto frequency;
    
    /**
     * 是否启用免打扰
     */
    private Boolean quietHoursEnabled;
    
    /**
     * 免打扰时间配置
     */
    private QuietHoursConfigDto quietHoursConfig;
    
    /**
     * 通知频率类型枚举
     */
    public enum FrequencyType {
        /** 实时通知 */
        REAL_TIME,
        /** 间隔通知 */
        INTERVAL,
        /** 批量通知 */
        BATCH,
        /** 每日摘要 */
        DAILY_SUMMARY
    }
    
    /**
     * 免打扰时间配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuietHoursConfigDto implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 开始时间（HH:mm格式）
         */
        private String startTime;
        
        /**
         * 结束时间（HH:mm格式）
         */
        private String endTime;
        
        /**
         * 生效的星期几（1-7，1为周一）
         */
        private List<Integer> daysOfWeek;
        
        /**
         * 时区
         */
        private String timezone = "Asia/Shanghai";
    }
}
