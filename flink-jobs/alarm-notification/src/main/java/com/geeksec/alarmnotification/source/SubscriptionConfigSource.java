package com.geeksec.alarmnotification.source;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.alarmnotification.client.AlarmServiceClient;
import com.geeksec.alarmnotification.dto.NotificationSubscriptionDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.source.SourceFunction;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 订阅配置数据源
 * 定期从告警服务获取订阅配置
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SubscriptionConfigSource implements SourceFunction<NotificationSubscriptionDto> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmServiceClient alarmServiceClient;
    private final int refreshIntervalSeconds;
    private final ObjectMapper objectMapper;
    
    private volatile boolean running = true;
    
    public SubscriptionConfigSource(AlarmServiceClient alarmServiceClient, int refreshIntervalSeconds) {
        this.alarmServiceClient = alarmServiceClient;
        this.refreshIntervalSeconds = refreshIntervalSeconds;
        this.objectMapper = new ObjectMapper();
    }
    
    @Override
    public void run(SourceContext<NotificationSubscriptionDto> ctx) throws Exception {
        log.info("订阅配置数据源启动，刷新间隔: {} 秒", refreshIntervalSeconds);
        
        while (running) {
            try {
                // 获取所有有效订阅
                CompletableFuture<List<NotificationSubscriptionDto>> future = 
                        alarmServiceClient.getAllActiveSubscriptions();
                
                List<NotificationSubscriptionDto> subscriptions = future.get();
                
                if (subscriptions != null && !subscriptions.isEmpty()) {
                    log.debug("获取到 {} 个订阅配置", subscriptions.size());
                    
                    // 发送每个订阅配置
                    for (NotificationSubscriptionDto subscription : subscriptions) {
                        if (running) {
                            ctx.collect(subscription);
                        }
                    }
                } else {
                    log.debug("未获取到任何订阅配置");
                }
                
                // 等待下次刷新
                if (running) {
                    Thread.sleep(refreshIntervalSeconds * 1000L);
                }
                
            } catch (InterruptedException e) {
                log.info("订阅配置数据源被中断");
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("获取订阅配置失败: {}", e.getMessage(), e);
                
                // 发生错误时等待较短时间后重试
                if (running) {
                    try {
                        Thread.sleep(30000); // 30秒后重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        log.info("订阅配置数据源已停止");
    }
    
    @Override
    public void cancel() {
        log.info("取消订阅配置数据源");
        running = false;
    }
    
    /**
     * 创建默认订阅配置数据源
     */
    public static SubscriptionConfigSource createDefault(String alarmServiceBaseUrl, int refreshIntervalSeconds) {
        AlarmServiceClient client = new AlarmServiceClient(alarmServiceBaseUrl);
        return new SubscriptionConfigSource(client, refreshIntervalSeconds);
    }
}
