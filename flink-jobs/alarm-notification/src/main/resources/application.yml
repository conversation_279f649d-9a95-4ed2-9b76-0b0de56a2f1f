# 告警通知作业配置
# NTA 3.0 - Alarm Notification Service

# ==================== 基础配置 ====================
alarm:
  notification:
    job:
      name: "alarm-notification-job"
      parallelism: 4
    
    # 检查点配置
    checkpoint:
      interval: 60000  # 60秒
      timeout: 300000  # 5分钟
      min-pause-between: 5000  # 5秒
      max-concurrent: 1
    
    # 并行度配置
    parallelism:
      kafka-source: 4
      processing: 8
      notification-sink: 2
      notification: 2
    
    # 输入配置
    input:
      topic: "processed-alarms"
    
    # 订阅配置
    subscription:
      changes:
        topic: "subscription-changes"
      refresh:
        interval:
          seconds: 300  # 5分钟
        periodic:
          enabled: true
    
    # 告警服务配置
    alarm:
      service:
        base:
          url: "http://localhost:8080"
    
    # 通知配置
    batch:
      size: 50
    linger:
      ms: 1000
    
    # 监控配置
    monitoring:
      enabled: true
      metrics:
        interval: 30000  # 30秒
      performance:
        logging: true
      detailed:
        metrics: false

# ==================== Kafka 配置 ====================
kafka:
  bootstrap:
    servers: "localhost:9092"
  
  consumer:
    group:
      id: "alarm-notification-consumer"
  
  starting:
    offsets: "latest"
  
  auto:
    commit: true
  
  commit:
    interval: 5000

# ==================== 邮件配置 ====================
alarm:
  notification:
    email:
      smtp:
        host: "localhost"
        port: 587
      username: ""
      password: ""
      from:
        address: "<EMAIL>"
        name: "NTA安全系统"
      tls:
        enabled: true
      connection:
        timeout: 10000
      read:
        timeout: 10000

# ==================== Kafka通知配置 ====================
alarm:
  notification:
    kafka:
      bootstrap:
        servers: "localhost:9092"
      acks: "1"
      retries: 3
      batch:
        size: 16384
      linger:
        ms: 1
      buffer:
        memory: 33554432
      request:
        timeout:
          ms: 30000
      connection:
        timeout:
          ms: 10000

# ==================== 日志配置 ====================
logging:
  level:
    com.geeksec.alarmnotification: INFO
    org.apache.flink: WARN
    org.apache.kafka: WARN
    org.springframework.mail: DEBUG
  
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  
  file:
    name: "/opt/flink/log/alarm-notification.log"
    max-size: "100MB"
    max-history: 30

# ==================== 开发环境配置 ====================
---
spring:
  profiles: dev

alarm:
  notification:
    alarm:
      service:
        base:
          url: "http://localhost:8080"
    
    monitoring:
      detailed:
        metrics: true
      performance:
        logging: true

kafka:
  bootstrap:
    servers: "localhost:9092"

logging:
  level:
    com.geeksec.alarmnotification: DEBUG

# ==================== 生产环境配置 ====================
---
spring:
  profiles: prod

alarm:
  notification:
    job:
      parallelism: 8
    
    parallelism:
      kafka-source: 8
      processing: 16
      notification-sink: 4
      notification: 4
    
    alarm:
      service:
        base:
          url: "http://alarm-service:8080"
    
    monitoring:
      detailed:
        metrics: false
      performance:
        logging: false

kafka:
  bootstrap:
    servers: "kafka-cluster:9092"

logging:
  level:
    com.geeksec.alarmnotification: INFO
    root: WARN

# ==================== 测试环境配置 ====================
---
spring:
  profiles: test

alarm:
  notification:
    job:
      parallelism: 2
    
    subscription:
      refresh:
        interval:
          seconds: 60  # 1分钟，测试环境更频繁
    
    monitoring:
      detailed:
        metrics: true

kafka:
  bootstrap:
    servers: "localhost:9092"

logging:
  level:
    com.geeksec.alarmnotification: DEBUG
