# Alarm Notification Service

告警通知服务，负责根据用户订阅配置发送告警通知。

## 概述

`alarm-notification` 是 NTA 3.0 系统中专门负责告警通知的 Flink 作业。它从 `alarm-processor` 接收处理后的告警数据，根据用户订阅配置匹配告警并发送通知。

## 架构设计

### 数据流

```
Kafka(processed-alarms) → 告警匹配 → 通知发送 → 结果记录
                              ↑
                        订阅配置管理
```

### 核心组件

1. **AlarmNotificationJob**: 主作业类
2. **NotificationPipeline**: 通知处理流水线
3. **NotificationFunction**: 告警匹配和通知处理函数
4. **NotificationSender**: 通知发送器（支持邮件和Kafka）
5. **AlarmServiceClient**: 告警服务客户端，获取订阅配置

## 功能特性

### 通知渠道
- **邮件通知**: 支持 SMTP 邮件发送
- **Kafka通知**: 发送消息到指定 Kafka 主题

### 订阅管理
- **实时配置更新**: 监听订阅配置变更事件
- **定期配置刷新**: 定期从告警服务获取最新配置
- **广播状态管理**: 使用 Flink Broadcast State 管理订阅配置

### 告警匹配
- **规则引擎**: 支持多种匹配操作符（等于、包含、正则等）
- **字段匹配**: 支持告警的各种字段匹配
- **组合条件**: 支持多个规则的 AND 逻辑组合

## 配置说明

### 主要配置项

```yaml
alarm:
  notification:
    job:
      name: "alarm-notification-job"
      parallelism: 4
    
    input:
      topic: "processed-alarms"  # 输入主题
    
    subscription:
      changes:
        topic: "subscription-changes"  # 订阅变更主题
    
    alarm:
      service:
        base:
          url: "http://localhost:8080"  # 告警服务地址
```

### 邮件配置

```yaml
alarm:
  notification:
    email:
      smtp:
        host: "localhost"
        port: 587
      username: ""
      password: ""
      from:
        address: "<EMAIL>"
        name: "NTA安全系统"
```

## 部署说明

### 构建

```bash
cd flink-jobs/alarm-notification
mvn clean package
```

### 运行

```bash
# 本地运行
flink run target/alarm-notification-3.0.0.jar

# 集群运行
flink run -m yarn-cluster target/alarm-notification-3.0.0.jar
```

### Docker 部署

```bash
# 构建镜像
docker build -t nta/alarm-notification:3.0.0 .

# 运行容器
docker run -d \
  --name alarm-notification \
  -e KAFKA_BOOTSTRAP_SERVERS=kafka:9092 \
  -e ALARM_SERVICE_URL=http://alarm-service:8080 \
  nta/alarm-notification:3.0.0
```

## 监控指标

### 关键指标
- `total_alarms`: 处理的告警总数
- `matched_subscriptions`: 匹配的订阅数
- `notification_sent`: 发送的通知数
- `notification_failed`: 发送失败的通知数

### 性能指标
- `processing_latency`: 处理延迟
- `notification_latency`: 通知发送延迟
- `subscription_refresh_time`: 订阅配置刷新时间

## 故障排查

### 常见问题

1. **告警未收到通知**
   - 检查订阅配置是否正确
   - 验证告警是否匹配订阅规则
   - 查看通知渠道配置

2. **邮件发送失败**
   - 检查 SMTP 服务器配置
   - 验证邮箱账号和密码
   - 查看网络连接

3. **Kafka 消息发送失败**
   - 检查 Kafka 集群状态
   - 验证主题是否存在
   - 查看生产者配置

### 日志查看

```bash
# 查看作业日志
tail -f /opt/flink/log/alarm-notification.log

# 查看 Flink 作业状态
flink list
flink show <job-id>
```

## 开发指南

### 添加新的通知渠道

1. 实现 `NotificationChannel` 接口
2. 在 `NotificationSender` 中添加处理逻辑
3. 更新配置类和 DTO

### 扩展匹配规则

1. 在 `SubscriptionRuleDto.OperatorType` 中添加新操作符
2. 在 `NotificationFunction.matchesRule` 中实现匹配逻辑

## 与其他服务的关系

### 上游依赖
- **alarm-processor**: 提供处理后的告警数据
- **services/alarm**: 提供订阅配置管理

### 下游影响
- **监控系统**: 接收通知发送结果
- **用户**: 接收告警通知

## 版本历史

- **3.0.0**: 初始版本，从 alarm-processor 中拆分出来
  - 支持邮件和 Kafka 通知
  - 实现订阅配置管理
  - 支持实时告警匹配
