# 告警订阅事件驱动架构指南

## 概述

本文档描述了告警订阅/通知功能的事件驱动架构实现，该架构通过 Kafka 事件实现订阅配置的实时同步，提高了系统的响应性和一致性。

## 架构设计

### 数据流向

```
用户操作 → services/alarm → 数据库更新 → Kafka事件发送
                                              ↓
告警事件 → alarm-processor ← 订阅配置同步 ← Kafka事件消费
                ↓
            匹配 + 发送通知
                ↓
            记录通知结果 → services/alarm
```

### 核心组件

#### 1. services/alarm 模块

**事件发布**：
- `SubscriptionChangeEvent`：订阅变更事件模型
- `SubscriptionEventPublisher`：事件发布服务
- 在订阅 CRUD 操作后自动发送 Kafka 事件

**主要事件类型**：
- `CREATED`：订阅创建
- `UPDATED`：订阅更新  
- `DELETED`：订阅删除
- `ENABLED`：订阅启用
- `DISABLED`：订阅禁用

#### 2. flink-jobs/alarm-processor 模块

**配置同步**：
- `SubscriptionConfigSource`：订阅配置数据源
- `EventDrivenNotificationPipeline`：事件驱动通知流水线
- 启动时获取全量配置，运行时接收增量变更

**处理流程**：
1. 启动时调用 REST API 获取全量订阅配置
2. 监听 Kafka 主题接收配置变更事件
3. 使用 Broadcast State 模式通知下游算子
4. 实时匹配告警和订阅规则，发送通知

## 配置说明

### services/alarm 配置

在 `application.yml` 中添加：

```yaml
nta:
  alarm:
    subscription:
      kafka:
        topic: subscription-changes
        bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
        producer:
          acks: 1
          retries: 3
          batch-size: 16384
          linger-ms: 1
          buffer-memory: 33554432
```

### alarm-processor 配置

在 `alarm-processor.properties` 中添加：

```properties
# 订阅配置同步
alarm.processor.notification.subscription.changes.topic=subscription-changes
alarm.processor.notification.subscription.consumer.group=alarm-processor-subscription-group
```

## 使用方式

### 1. 启动服务

```bash
# 启动 services/alarm
cd services/alarm
mvn spring-boot:run

# 启动 alarm-processor
cd flink-jobs/alarm-processor
mvn exec:java -Dexec.mainClass="com.geeksec.alarmprocessor.job.AlarmProcessorJob"
```

### 2. 创建订阅

通过 REST API 创建订阅时，系统会自动：
1. 保存订阅到数据库
2. 发送 `CREATED` 事件到 Kafka
3. alarm-processor 接收事件并更新内存状态

```bash
curl -X POST http://localhost:8080/alarm/subscription \
  -H "Content-Type: application/json" \
  -d '{
    "subscriptionName": "高危恶意软件告警",
    "description": "监控高危级别的恶意软件告警",
    "matchRules": [
      {
        "fieldName": "alarmType",
        "operator": "EQUALS",
        "expectedValue": "恶意软件"
      }
    ],
    "notificationChannels": [
      {
        "channelType": "EMAIL",
        "address": "<EMAIL>",
        "enabled": true
      }
    ]
  }'
```

### 3. 更新订阅

更新订阅时会发送 `UPDATED` 事件：

```bash
curl -X PUT http://localhost:8080/alarm/subscription/{id} \
  -H "Content-Type: application/json" \
  -d '{
    "subscriptionName": "更新后的订阅名称",
    "enabled": true
  }'
```

### 4. 删除订阅

删除订阅时会发送 `DELETED` 事件：

```bash
curl -X DELETE http://localhost:8080/alarm/subscription/{id}
```

## 事件格式

### Kafka 事件结构

```json
{
  "eventId": "sub_event_1703123456789_123",
  "subscriptionId": "subscription-001",
  "userId": "user123",
  "eventType": "CREATED",
  "eventTime": "2023-12-21T10:30:45",
  "subscriptionData": "{\"subscriptionId\":\"subscription-001\",\"enabled\":true,...}",
  "operatedBy": "admin",
  "description": "创建订阅"
}
```

### 订阅数据格式

```json
{
  "subscriptionId": "subscription-001",
  "userId": "user123",
  "username": "admin",
  "subscriptionName": "高危恶意软件告警",
  "enabled": true,
  "priorityLevel": 1,
  "rules": [
    {
      "fieldName": "alarmType",
      "operator": "EQUALS",
      "expectedValue": "恶意软件",
      "ignoreCase": false
    }
  ],
  "channels": [
    {
      "channelType": "EMAIL",
      "address": "<EMAIL>",
      "templateId": "default_email_template",
      "enabled": true
    }
  ],
  "frequencyType": "REAL_TIME",
  "createTime": "2023-12-21T10:30:45",
  "updateTime": "2023-12-21T10:30:45"
}
```

## 监控和运维

### 1. 监控指标

- Kafka 消息生产/消费延迟
- 订阅配置同步状态
- 通知发送成功率
- 事件处理错误率

### 2. 日志查看

```bash
# 查看 services/alarm 事件发布日志
grep "发布订阅.*事件" services/alarm/logs/application.log

# 查看 alarm-processor 事件消费日志
grep "收到订阅变更事件" flink-jobs/alarm-processor/logs/alarm-processor.log
```

### 3. 故障排查

**常见问题**：

1. **Kafka 连接失败**
   - 检查 Kafka 服务状态
   - 验证配置中的 bootstrap-servers

2. **事件消费延迟**
   - 检查消费者组状态
   - 监控 Kafka 主题分区

3. **订阅配置不同步**
   - 检查事件发送是否成功
   - 验证消费者是否正常运行

## 优势

1. **实时性**：配置变更立即通过 Kafka 事件传播
2. **可靠性**：Kafka 保证消息的持久化和可靠传递
3. **扩展性**：支持多个 alarm-processor 实例并行处理
4. **一致性**：通过事件驱动确保配置的最终一致性
5. **可观测性**：完整的事件日志便于监控和调试

## 注意事项

1. **事件顺序**：同一订阅的事件按时间顺序处理
2. **幂等性**：处理重复事件时保证幂等性
3. **容错处理**：网络异常时的重试和降级机制
4. **性能考虑**：大量订阅变更时的批处理优化
5. **数据一致性**：确保数据库和内存状态的一致性

## 后续优化

1. 实现事件的批量处理
2. 添加事件重试和死信队列
3. 支持订阅配置的版本管理
4. 实现更细粒度的配置变更通知
5. 添加配置同步的健康检查机制
