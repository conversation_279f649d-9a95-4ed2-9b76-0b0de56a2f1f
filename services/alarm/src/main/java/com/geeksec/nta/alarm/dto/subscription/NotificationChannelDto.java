package com.geeksec.nta.alarm.dto.subscription;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * 通知渠道 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationChannelDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 渠道ID
     */
    private String channelId;
    
    /**
     * 渠道名称
     */
    private String channelName;
    
    /**
     * 渠道类型
     */
    @NotNull(message = "渠道类型不能为空")
    private ChannelType channelType;
    
    /**
     * 是否启用
     */
    private Boolean enabled = true;
    
    /**
     * 接收地址（邮箱、Kafka Topic等）
     */
    @NotBlank(message = "接收地址不能为空")
    private String address;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 渠道配置参数
     */
    private Map<String, Object> config;
    
    /**
     * 优先级
     */
    private Integer priority = 1;
    
    /**
     * 超时时间（毫秒）
     */
    private Long timeoutMs = 30000L;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 3;
    
    /**
     * 重试间隔（毫秒）
     */
    private Long retryIntervalMs = 1000L;
    
    /**
     * 渠道类型枚举
     */
    public enum ChannelType {
        /** 邮件 */
        EMAIL("email", "邮件通知"),
        /** Kafka消息 */
        KAFKA("kafka", "Kafka消息");
        
        private final String code;
        private final String displayName;
        
        ChannelType(String code, String displayName) {
            this.code = code;
            this.displayName = displayName;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public static ChannelType fromCode(String code) {
            for (ChannelType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
    
    /**
     * 创建邮件渠道
     */
    public static NotificationChannelDto createEmailChannel(String email, String templateId) {
        return NotificationChannelDto.builder()
                .channelType(ChannelType.EMAIL)
                .address(email)
                .templateId(templateId)
                .enabled(true)
                .priority(1)
                .timeoutMs(30000L)
                .retryCount(3)
                .retryIntervalMs(1000L)
                .build();
    }
    
    /**
     * 创建Kafka渠道
     */
    public static NotificationChannelDto createKafkaChannel(String topic, String templateId) {
        return NotificationChannelDto.builder()
                .channelType(ChannelType.KAFKA)
                .address(topic)
                .templateId(templateId)
                .enabled(true)
                .priority(2)
                .timeoutMs(10000L)
                .retryCount(2)
                .retryIntervalMs(500L)
                .build();
    }
}
