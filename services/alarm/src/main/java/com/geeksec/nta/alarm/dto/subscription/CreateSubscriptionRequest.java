package com.geeksec.nta.alarm.dto.subscription;

import com.geeksec.nta.alarm.entity.AlarmSubscription.FrequencyType;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建订阅请求 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class CreateSubscriptionRequest {
    
    /**
     * 订阅名称
     */
    @NotBlank(message = "订阅名称不能为空")
    @Size(max = 100, message = "订阅名称长度不能超过100个字符")
    private String subscriptionName;
    
    /**
     * 订阅描述
     */
    @Size(max = 500, message = "订阅描述长度不能超过500个字符")
    private String description;
    
    /**
     * 优先级（1-5）
     */
    private Integer priorityLevel = 1;
    
    /**
     * 匹配规则列表
     */
    @NotEmpty(message = "匹配规则不能为空")
    @Valid
    private List<SubscriptionRuleDto> matchRules;
    
    /**
     * 通知渠道列表
     */
    @NotEmpty(message = "通知渠道不能为空")
    @Valid
    private List<NotificationChannelDto> notificationChannels;
    
    /**
     * 通知频率类型
     */
    @NotNull(message = "通知频率类型不能为空")
    private FrequencyType frequencyType = FrequencyType.REAL_TIME;
    
    /**
     * 频率控制配置
     */
    @Valid
    private FrequencyConfigDto frequencyConfig;
    
    /**
     * 是否启用免打扰
     */
    private Boolean quietHoursEnabled = false;
    
    /**
     * 免打扰时间配置
     */
    @Valid
    private QuietHoursConfigDto quietHoursConfig;
}
