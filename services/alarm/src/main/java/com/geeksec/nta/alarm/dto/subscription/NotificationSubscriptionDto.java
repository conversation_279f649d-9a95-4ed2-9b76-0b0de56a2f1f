package com.geeksec.nta.alarm.dto.subscription;

import com.geeksec.nta.alarm.entity.AlarmSubscription.FrequencyType;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 通知订阅 DTO（供 alarm-processor 使用）
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationSubscriptionDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅ID
     */
    private String subscriptionId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 订阅名称
     */
    private String subscriptionName;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 优先级
     */
    private Integer priorityLevel;
    
    /**
     * 匹配规则列表
     */
    private List<SubscriptionRuleDto> rules;
    
    /**
     * 通知渠道列表
     */
    private List<NotificationChannelDto> channels;
    
    /**
     * 通知频率类型
     */
    private FrequencyType frequencyType;
    
    /**
     * 频率控制配置
     */
    private FrequencyConfigDto frequency;
    
    /**
     * 免打扰时间设置
     */
    private QuietHoursConfigDto quietHours;
    
    /**
     * 触发次数
     */
    private Integer triggerCount;
    
    /**
     * 最后触发时间
     */
    private LocalDateTime lastTriggeredTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
