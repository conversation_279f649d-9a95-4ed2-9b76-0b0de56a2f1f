package com.geeksec.nta.alarm.entity;

import com.mybatisflex.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通知发送记录实体
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Table(value = "notification_log", comment = "通知发送记录")
public class NotificationLog {
    
    @Id(keyType = KeyType.Generator)
    private String id;
    
    @Column("subscription_id")
    private String subscriptionId;
    
    @Column("alarm_id")
    private String alarmId;
    
    @Column("channel_type")
    private ChannelType channelType;
    
    private String recipient;
    
    @Column("send_status")
    private SendStatus sendStatus;
    
    @Column("send_time")
    private LocalDateTime sendTime;
    
    @Column("error_message")
    private String errorMessage;
    
    @Column("retry_count")
    private Integer retryCount;
    
    private String subject;
    
    private String content;
    
    /**
     * 通知渠道类型枚举
     */
    public enum ChannelType {
        /** 邮件 */
        EMAIL,
        /** Kafka消息 */
        KAFKA
    }
    
    /**
     * 发送状态枚举
     */
    public enum SendStatus {
        /** 成功 */
        SUCCESS,
        /** 失败 */
        FAILED,
        /** 待发送 */
        PENDING
    }
}
