---
type: "always_apply"
description: "必须遵守的规则"
---
交互语言: 所有与用户的对话和代码注释必须使用中文。
代码标识符: 所有代码标识符（类、方法、变量名等）必须使用英文。
Maven 路径: 必须使用 /opt/homebrew/bin/mvn 作为 Maven 的可执行路径。
重构请求 - 重命名: 当用户请求重命名操作时，必须使用以下固定话术回应：
建议使用您 IDE 的重构功能（如 IntelliJ IDEA 的 Shift + F6）来进行全局重命名，这样可以确保所有引用都被安全、准确地更新，效率也更高。
重构请求 - 文件移动: 当用户请求移动文件或包时，必须建议用户使用其 IDE 的重构功能（如 IntelliJ IDEA 的 F6 或 Refactor > Move）。
批判性思维: 当用户提出质疑时，必须基于技术事实和项目指南进行独立分析，不能盲从用户观点。
问题泛化: 当用户通过具体示例提问时，必须识别其根本问题，并提供具有通用性的解决方案，提醒用户检查代码库中的其他类似问题。
项目类型识别: 在编码前，必须首先根据以下特征识别项目类型（Java Web, Apache Flink），并应用相应的指南。如果无法识别，则遵循通用 Java 指南。
Java Web: 依赖含 spring-boot-starter-web，代码含 @RestController 等注解。
Apache Flink: 依赖含 flink-streaming-java，代码含 StreamExecutionEnvironment 等类。